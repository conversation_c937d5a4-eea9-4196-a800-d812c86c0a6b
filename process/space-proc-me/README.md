# Space Proc ME Service

This is the Space Proc ME microservice, part of the Space middleware ecosystem.

## Features

- MongoDB ODM integration with shared document definitions
- Legacy MongoDB Atlas API support
- RESTful API endpoints
- Comprehensive logging and error handling

## Requirements

- PHP 8.0+
- Symfony 6.4
- MongoDB (local or Atlas)
- Composer

## Installation

1. Install dependencies:
   ```bash
   composer install
   ```

2. Configure environment variables in `.env.local`:
   ```bash
   # MongoDB ODM
   MONGO_DB_URL=mongodb://localhost:27017
   MONGODB_DB=space

   # Other service URLs
   MS_SYS_IDP_URL=https://your-idp-service.com
   MS_SYS_APDV_URL=https://your-apdv-service.com
   MS_SYS_SA_URL=https://your-sa-service.com
   MS_SYS_USER_DB_URL=https://your-user-db-service.com
   ```

3. Start local MongoDB (using Docker):
   ```bash
   docker compose up -d
   ```

4. Start the development server:
   ```bash
   symfony server:start
   ```

## MongoDB ODM Integration

This service uses the shared `space-mongo-documents` package for MongoDB document definitions and ODM integration.

### Configuration

The MongoDB connection is configured in the `.env` file:

```
# MongoDB ODM
MONGO_DB_URL=mongodb://localhost:27017
MONGODB_DB=space
```

### Available Document Classes

The following document classes are available from the shared module:

- `UserData`: Represents user data with vehicles
- `Vehicle`: Represents a vehicle
- `UserPsaId`: Represents a user's PSA ID
- `DrivingScore`: Represents driving score data
- `Settings`: Represents application settings

### Using MongoDB ODM

#### Injecting the MongoDB Service

```php
use App\Service\MongoDBService;

class YourService
{
    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }
}
```

#### Finding Documents

```php
// Find a user by ID
$user = $this->mongoDBService->findUserById('user123');

// Find users by VIN
$users = $this->mongoDBService->findUsersByVin('VIN123456789');

// Find driving scores by VIN
$drivingScores = $this->mongoDBService->findDrivingScoresByVin('VIN123456789');

// Generic find methods
$user = $this->mongoDBService->findOneBy(UserData::class, ['userId' => 'user123']);
$users = $this->mongoDBService->findBy(UserData::class, ['vehicle.vin' => 'VIN123']);
```

#### Creating and Saving Documents

```php
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;

// Create a new user
$userData = new UserData();
$userData->setUserId('user123');

// Create a vehicle
$vehicle = new Vehicle();
$vehicle->setVin('VIN123456789');
$vehicle->setBrand('DS');
$vehicle->setModel('DS3');

// Add the vehicle to the user
$userData->addVehicle($vehicle);

// Save the user
$this->mongoDBService->save($userData);
```

#### Updating Documents

```php
// Find a user
$user = $this->mongoDBService->findUserById('user123');

// Update the user
$user->setUserId('user456');

// Save the changes
$this->mongoDBService->update($user);
```

#### Removing Documents

```php
// Find a user
$user = $this->mongoDBService->findUserById('user123');

// Remove the user
$this->mongoDBService->remove($user);
```

### API Endpoints

The following API endpoints are available for testing MongoDB ODM:

- `GET /api/mongodb/users/{userId}`: Get user data by user ID
- `GET /api/mongodb/vehicles/{vin}`: Get vehicle data by VIN
- `GET /api/mongodb/driving-scores/{vin}`: Get driving scores by VIN
- `POST /api/mongodb/users`: Create a new user with vehicles
- `POST /api/mongodb/driving-scores`: Create a new driving score

## Legacy MongoDB Atlas API

The service also maintains support for the legacy MongoDB Atlas API through the `MongoAtlasQueryService`.

### Configuration

```
# MongoDB Atlas API (legacy)
MONGO_ATLAS_BASE_URL=https://your-atlas-api.com
MONGO_ATLAS_API_KEY=your-api-key
MONGO_APP=your-app-id
MONGO_DATABASE=your-database
MONGO_DATASOURCE=your-datasource
```

## Development

### Running Tests

```bash
php bin/phpunit
```

### Code Quality

```bash
# Run PHPStan
vendor/bin/phpstan analyse

# Run PHP-CS-Fixer
vendor/bin/php-cs-fixer fix
```

### API Documentation

API documentation is available at `/api/doc` when running in development mode.

## Docker Support

The service includes Docker Compose configuration for local MongoDB:

```bash
# Start MongoDB container
docker compose up -d mongodb

# Stop MongoDB container
docker compose down
```

## Project Milestones

- [x] MongoDB ODM integration (space-mongo-documents)
- [x] MongoDB service wrapper implementation
- [x] API endpoints for testing
- [x] Docker Compose configuration
- [x] Documentation and examples
- [ ] Unit tests for MongoDB service
- [ ] Integration tests for API endpoints
