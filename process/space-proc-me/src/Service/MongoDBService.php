<?php

namespace App\Service;

use Doctrine\ODM\MongoDB\DocumentManager;
use Psr\Log\LoggerInterface;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\DrivingScore;
use Space\MongoDocuments\Document\Settings;

class MongoDBService
{
    public function __construct(
        private DocumentManager $documentManager,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Generic find one by criteria
     */
    public function findOneBy(string $documentClass, array $criteria): ?object
    {
        try {
            return $this->documentManager->getRepository($documentClass)->findOneBy($criteria);
        } catch (\Exception $e) {
            $this->logger->error('Error finding document by criteria', [
                'documentClass' => $documentClass,
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Generic find by criteria
     */
    public function findBy(string $documentClass, array $criteria, ?array $orderBy = null, ?int $limit = null, ?int $offset = null): array
    {
        try {
            return $this->documentManager->getRepository($documentClass)->findBy($criteria, $orderBy, $limit, $offset);
        } catch (\Exception $e) {
            $this->logger->error('Error finding documents by criteria', [
                'documentClass' => $documentClass,
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Find a user by ID
     */
    public function findUserById(string $userId): ?UserData
    {
        try {
            return $this->documentManager->getRepository(UserData::class)->findOneBy(['userId' => $userId]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding user by ID', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Find users by VIN
     */
    public function findUsersByVin(string $vin): array
    {
        try {
            return $this->documentManager->createQueryBuilder(UserData::class)
                ->field('vehicle.vin')->equals($vin)
                ->getQuery()
                ->execute()
                ->toArray();
        } catch (\Exception $e) {
            $this->logger->error('Error finding users by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Find user by user ID and VIN
     */
    public function findUserByUserIdAndVin(string $userId, string $vin): ?UserData
    {
        try {
            return $this->documentManager->createQueryBuilder(UserData::class)
                ->field('userId')->equals($userId)
                ->field('vehicle.vin')->equals($vin)
                ->getQuery()
                ->getSingleResult();
        } catch (\Exception $e) {
            $this->logger->error('Error finding user by user ID and VIN', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Find driving scores by VIN
     */
    public function findDrivingScoresByVin(string $vin): array
    {
        try {
            return $this->documentManager->getRepository(DrivingScore::class)->findBy(['vin' => $vin]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving scores by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Find settings by complex filter (delegates to space-mongo-documents service)
     */
    public function findSettingsByFilter(array $filter): ?Settings
    {
        try {
            $this->logger->info('MongoDBService: Delegating findSettingsByFilter to repository', [
                'filter' => $filter
            ]);

            $repository = $this->documentManager->getRepository(Settings::class);

            if (method_exists($repository, 'findByComplexFilter')) {
                return $repository->findByComplexFilter($filter);
            }

            // Fallback to basic findOneBy if complex filter method doesn't exist
            $basicFilter = [];
            if (isset($filter['brand'])) {
                $basicFilter['brand'] = $filter['brand'];
            }
            if (isset($filter['source'])) {
                $basicFilter['source'] = $filter['source'];
            }
            if (isset($filter['culture'])) {
                $basicFilter['culture'] = $filter['culture'];
            }

            return $repository->findOneBy($basicFilter);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by filter', [
                'filter' => $filter,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Find settings by type, brand, and country
     */
    public function findSettingsByTypeBrandAndCountry(string $type, string $brand, string $country): ?Settings
    {
        try {
            return $this->documentManager->getRepository(Settings::class)->findOneBy([
                'type' => $type,
                'brand' => $brand,
                'country' => $country
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Save a document
     */
    public function save($document): void
    {
        try {
            $this->documentManager->persist($document);
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error saving document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update a document
     */
    public function update($document): void
    {
        try {
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error updating document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove a document
     */
    public function remove($document): void
    {
        try {
            $this->documentManager->remove($document);
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error removing document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get the document manager
     */
    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }
}
