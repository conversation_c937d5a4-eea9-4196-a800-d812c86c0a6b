<?php

namespace App\Controller;

use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Document\UserPsaId;
use Space\MongoDocuments\Document\DrivingScore;
use App\Service\MongoDBService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;

#[Route('/api/mongodb')]
class MongoDBController extends AbstractController
{
    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    #[Route('/users/{userId}', name: 'app_mongodb_get_user', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns user data',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'userId', type: 'string'),
                new OA\Property(property: 'vehicles', type: 'array', items: new OA\Items(type: 'object')),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'userId',
        description: 'User ID',
        in: 'path',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'MongoDB')]
    public function getUserData(string $userId): JsonResponse
    {
        $user = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
        }

        return new JsonResponse([
            'id' => $user->getId(),
            'userId' => $user->getUserId(),
            'vehicles' => array_map(function (Vehicle $vehicle) {
                return [
                    'vin' => $vehicle->getVin(),
                    'brand' => $vehicle->getBrand(),
                    'model' => $vehicle->getModel(),
                    'version' => $vehicle->getVersion(),
                ];
            }, $user->getVehicles()),
        ]);
    }

    #[Route('/vehicles/{vin}', name: 'app_mongodb_get_vehicle', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns vehicle data',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                type: 'object',
                properties: [
                    new OA\Property(property: 'userId', type: 'string'),
                    new OA\Property(property: 'vin', type: 'string'),
                    new OA\Property(property: 'brand', type: 'string'),
                    new OA\Property(property: 'model', type: 'string'),
                ]
            )
        )
    )]
    #[OA\Parameter(
        name: 'vin',
        description: 'Vehicle Identification Number',
        in: 'path',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'MongoDB')]
    public function getVehicle(string $vin): JsonResponse
    {
        $users = $this->mongoDBService->findBy(UserData::class, ['vehicle.vin' => $vin]);

        if (empty($users)) {
            return new JsonResponse(['error' => 'Vehicle not found'], Response::HTTP_NOT_FOUND);
        }

        $result = [];
        foreach ($users as $user) {
            foreach ($user->getVehicles() as $vehicle) {
                if ($vehicle->getVin() === $vin) {
                    $result[] = [
                        'userId' => $user->getUserId(),
                        'vin' => $vehicle->getVin(),
                        'brand' => $vehicle->getBrand(),
                        'model' => $vehicle->getModel(),
                        'version' => $vehicle->getVersion(),
                    ];
                }
            }
        }

        return new JsonResponse($result);
    }

    #[Route('/driving-scores/{vin}', name: 'app_mongodb_get_driving_scores', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns driving scores for a vehicle',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                type: 'object',
                properties: [
                    new OA\Property(property: 'id', type: 'string'),
                    new OA\Property(property: 'vin', type: 'string'),
                    new OA\Property(property: 'globalScore', type: 'integer'),
                    new OA\Property(property: 'accelerationScore', type: 'integer'),
                    new OA\Property(property: 'brakingScore', type: 'integer'),
                ]
            )
        )
    )]
    #[OA\Parameter(
        name: 'vin',
        description: 'Vehicle Identification Number',
        in: 'path',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'MongoDB')]
    public function getDrivingScores(string $vin): JsonResponse
    {
        $drivingScores = $this->mongoDBService->findBy(DrivingScore::class, ['vin' => $vin]);

        if (empty($drivingScores)) {
            return new JsonResponse(['error' => 'Driving scores not found'], Response::HTTP_NOT_FOUND);
        }

        $result = [];
        foreach ($drivingScores as $drivingScore) {
            $result[] = [
                'id' => $drivingScore->getId(),
                'vin' => $drivingScore->getVin(),
                'globalScore' => $drivingScore->getGlobalScore(),
                'accelerationScore' => $drivingScore->getAccelerationScore(),
                'brakingScore' => $drivingScore->getBrakingScore(),
                'lastUpdate' => $drivingScore->getLastUpdate() ? $drivingScore->getLastUpdate()->format('Y-m-d H:i:s') : null,
            ];
        }

        return new JsonResponse($result);
    }

    #[Route('/users', name: 'app_mongodb_create_user', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'User data to create',
        required: true,
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'userId', type: 'string', example: 'user123'),
                new OA\Property(
                    property: 'vehicles',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'vin', type: 'string', example: 'VR3UPHNKSKT101603'),
                            new OA\Property(property: 'brand', type: 'string', example: 'DS'),
                            new OA\Property(property: 'model', type: 'string', example: 'DS3'),
                            new OA\Property(property: 'version', type: 'string', example: 'Crossback E-Tense'),
                            new OA\Property(property: 'registrationNumber', type: 'string', example: 'AB-123-CD'),
                            new OA\Property(property: 'color', type: 'string', example: 'Blue'),
                            new OA\Property(property: 'energy', type: 'string', example: 'Electric'),
                            new OA\Property(property: 'status', type: 'string', example: 'Active'),
                            new OA\Property(property: 'registrationDate', type: 'string', format: 'date', example: '2023-01-15'),
                        ]
                    )
                ),
                new OA\Property(
                    property: 'userPsaId',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'psaId', type: 'string', example: 'PSA123456'),
                            new OA\Property(property: 'brand', type: 'string', example: 'DS'),
                        ]
                    )
                ),
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'User created successfully',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'message', type: 'string'),
                new OA\Property(property: 'userId', type: 'string'),
            ]
        )
    )]
    #[OA\Tag(name: 'MongoDB')]
    public function createUser(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data || !isset($data['userId'])) {
                return new JsonResponse(['error' => 'Invalid data or missing userId'], Response::HTTP_BAD_REQUEST);
            }

            // Check if user already exists
            $existingUser = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $data['userId']]);
            if ($existingUser) {
                return new JsonResponse(['error' => 'User already exists'], Response::HTTP_CONFLICT);
            }

            $userData = new UserData();
            $userData->setUserId($data['userId']);

            // Add vehicles if provided
            if (isset($data['vehicles']) && is_array($data['vehicles'])) {
                foreach ($data['vehicles'] as $vehicleData) {
                    $vehicle = new Vehicle();
                    $vehicle->setVin($vehicleData['vin'] ?? '');
                    $vehicle->setBrand($vehicleData['brand'] ?? '');
                    $vehicle->setModel($vehicleData['model'] ?? '');
                    $vehicle->setVersion($vehicleData['version'] ?? '');
                    $vehicle->setRegistrationNumber($vehicleData['registrationNumber'] ?? '');
                    $vehicle->setColor($vehicleData['color'] ?? '');
                    $vehicle->setEnergy($vehicleData['energy'] ?? '');
                    $vehicle->setStatus($vehicleData['status'] ?? 'Active');

                    if (isset($vehicleData['registrationDate'])) {
                        $vehicle->setRegistrationDate(new \DateTime($vehicleData['registrationDate']));
                    }

                    $userData->addVehicle($vehicle);
                }
            }

            // Add userPsaId if provided
            if (isset($data['userPsaId']) && is_array($data['userPsaId'])) {
                $userPsaIds = [];
                foreach ($data['userPsaId'] as $psaData) {
                    $userPsaId = new UserPsaId();
                    $userPsaId->setPsaId($psaData['psaId'] ?? '');
                    $userPsaId->setBrand($psaData['brand'] ?? '');
                    $userPsaIds[] = $userPsaId;
                }
                $userData->setUserPsaId($userPsaIds);
            }

            $this->mongoDBService->save($userData);

            return new JsonResponse([
                'message' => 'User created successfully',
                'userId' => $data['userId']
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Failed to create user: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/driving-scores', name: 'app_mongodb_create_driving_score', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Driving score data to create',
        required: true,
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'vin', type: 'string', example: 'VR3UPHNKSKT101603'),
                new OA\Property(property: 'contractNumber', type: 'string', example: 'CONTRACT123456'),
                new OA\Property(property: 'validFlag', type: 'string', example: 'Y'),
                new OA\Property(property: 'globalScore', type: 'integer', example: 85),
                new OA\Property(property: 'accelerationScore', type: 'integer', example: 78),
                new OA\Property(property: 'brakingScore', type: 'integer', example: 92),
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Driving score created successfully',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'message', type: 'string'),
                new OA\Property(property: 'vin', type: 'string'),
            ]
        )
    )]
    #[OA\Tag(name: 'MongoDB')]
    public function createDrivingScore(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data || !isset($data['vin'])) {
                return new JsonResponse(['error' => 'Invalid data or missing vin'], Response::HTTP_BAD_REQUEST);
            }

            $drivingScore = new DrivingScore();
            $drivingScore->setVin($data['vin']);
            $drivingScore->setContractNumber($data['contractNumber'] ?? '');
            $drivingScore->setValidFlag($data['validFlag'] ?? 'Y');
            $drivingScore->setGlobalScore($data['globalScore'] ?? 0);
            $drivingScore->setAccelerationScore($data['accelerationScore'] ?? 0);
            $drivingScore->setBrakingScore($data['brakingScore'] ?? 0);
            $drivingScore->setLastUpdate(new \DateTime());

            $this->mongoDBService->save($drivingScore);

            return new JsonResponse([
                'message' => 'Driving score created successfully',
                'vin' => $data['vin']
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Failed to create driving score: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
