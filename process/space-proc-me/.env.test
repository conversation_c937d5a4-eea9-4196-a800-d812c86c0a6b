# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

# MongoDB Atlas API (legacy)
MONGO_ATLAS_BASE_URL=https://example-10.example.data.mongodb-api.com
MONGO_ATLAS_API_KEY=your-api-key
MONGO_APP=your-app-id
MONGO_DATABASE=your-database
MONGO_DATASOURCE=your-datasource

# MongoDB ODM
MONGO_DB_URL=mongodb://localhost:27017
MONGODB_DB=space